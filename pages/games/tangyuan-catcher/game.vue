<template>
    <view>
        <view class="game-tips-msg-bar">
            <game-top-msg-bar right-icon="icon-football">
                <template v-slot:leftMsg>{{ countdown }}</template>
                <template v-slot:rightMsg>{{ score }}</template>
            </game-top-msg-bar>
        </view>

        <view class="game-container">
            <!-- 背景 -->
            <image class="background-image" :src="images.background" mode="widthFix"/>
        </view>

        <game-tips-popup ref="gameTipsPopup" :tips-list="tipsList" :show-ad="showAD" @startGame="startGame"/>

        <ready-countdown ref="readyCountdown" @countdownOver="countdownOver"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>

        <xwy-ad v-if="showAD" :ad_type="3"/>
    </view>
</template>

<script>
import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import readyCountdown from '../components/ready-countdown.vue'

const BASE_URL = 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/game/tangyuan/'

export default {
    components: {gameTipsPopup, gameTopMsgBar, readyCountdown},
    data() {
        return {
            // 游戏图片资源
            images: {
                background: `${BASE_URL}bg.jpg`,
                tangyuan: `${BASE_URL}tangyuan.png`,
                bowl: `${BASE_URL}bowl.png`
            },
            gameState: 'ready', // 游戏状态：ready  //starting  // gameover

            per_integral: 0,
            unit: '积分',
            seconds: 30,
            countdown: 30,
            score: 0,
            showAD: false
        }
    },

    computed: {
        tipsList() {
            return [
                '请用手指按住滚动的汤圆并拖到碗中。',
                `游戏倒计时${this.seconds}秒结束后结算${this.unit}，每抓住1个汤圆可获得${this.per_integral}${this.unit}。`
            ]
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.unit = params.unit
        this.per_integral = Number(params.per_integral)
        this.seconds = Number(params.seconds)
        this.countdown = this.seconds
        if (params.show_ad) this.showAD = true
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.initGame()
    },

    // 页面销毁时清除定时器
    onUnload() {
        this.clearCountdown()
    },


    methods: {
        async initGame() {
            await this.getGameData()

            this.$refs.gameTipsPopup.open()
        },

        async getGameData() {
            const data = await this.getOpenerEventChannelData() || {}

            if (data.bg_img) this.images.background = data.bg_img

            if (data.navigation_bar) uni.setNavigationBarColor({
                ...data.navigation_bar,
                fail: err => console.log(err)
            })
        },

        getOpenerEventChannelData() {
            return new Promise(resolve => {
                if (!this.getOpenerEventChannel?.()?.once) resolve({})
                this.getOpenerEventChannel().once('data', data => resolve(data))
            })
        },

        startGame() {
            this.$refs.gameTipsPopup.close()
            this.$refs.readyCountdown.open()
        },

        countdownOver() {
            this.startCountdown()
        },

        startCountdown() {
            this.countdown = this.seconds
            this.countdownInterval = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) this.gameOver()
            }, 1000)
        },

        clearCountdown() {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval)
                this.countdownInterval = null
            }
        },



        gameOver() {
            this.gameState = 'gameover'
            this.clearCountdown()

            this.submitResult()
        },

        async submitResult() {
            const count = this.score
            const sign = {
                // types: 38,
                point_id: this.point_id,
                result: count ? 'success' : 'fail',
                count
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        }
    }
}
</script>

<style lang="scss">
.game-tips-msg-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
}

.background-image {
    width: 100vw;
    height: 100vh;
    display: block;
}
</style>