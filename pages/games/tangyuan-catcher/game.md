## 抓汤圆（<PERSON>yuan Catcher）— 需求文档 v1.0

### 1. 概述
- **核心玩法**: 汤圆从屏幕右侧持续滚动到左侧，玩家按住并拖动汤圆，将其放入底部的碗中即可得分。
- **局内规则**: 仅计时与得分，无生命机制，未入碗不惩罚；倒计时结束即结算。
- **时长**: 每局 30s（固定，不可暂停）。

### 2. 平台与技术
- **目标平台**: 仅 MP-WEIXIN（微信小程序）
- **框架**: UniApp（Vue2）
- **实现限制**: 避免使用 `canvas`（小程序端性能欠佳），不使用悬浮控件，不提供暂停与音效
- **性能目标**: 主流机型稳定 ≥ 30 FPS；首屏关键资源加载 ≤ 2s（良好网络）

### 3. 玩法与规则
- **倒计时**: 进入游戏后直接开始 30s 计时；期间不允许暂停
- **汤圆生成**:
    - 初始生成间隔：1200ms；最小生成间隔：500ms（随时间逐步降低）
    - 生成位置：从右侧屏幕外进入；垂直位置 Y 在屏幕高度的 1/5 处开始，结束于“碗上边缘 − 汤圆高度 − 动态安全距离”
    - Y 范围计算：Ymin = max(屏幕高度×0.2, 顶部栏底部Y)；Ymax = 碗上边缘Y − 汤圆渲染高度 − GuardEff
    - 动态安全距离 GuardEff = max(基础边距, 汤圆尺寸×0.75, 屏幕高度×4%)；默认基础边距=36px（可在 `config.spawnEndGuardPx` 调整）
    - 最大同屏数量：建议 ≤ 8（控制 DOM 数量，保障性能）
- **移动与拖拽**:
    - 默认水平左移；随时间速度提升
    - 按住汤圆进入拖拽态，跟随手指移动；松手后若未入碗则继续左移
- **得分与失败**:
    - 入碗判定成功：+1 分（固定分值）；汤圆将停留在碗内，保持可见，且不可再次拖拽或移动
    - 未入碗并越过左边界：直接移除，不扣分
    - 无连击、无特殊汤圆，所有汤圆大小一致
- **结算**: 倒计时归零触发结算，展示本局得分

### 4. 难度曲线（示例，可按常量调参）
- **速度提升**: 横向速度从 160 px/s 线性提升至约 360 px/s
 - **速度提升**: 横向速度从 160 px/s 提升至 520 px/s，采用幂函数曲线：v = v0 + (vmax − v0) × progress^gamma（默认 gamma=1.6，中期更快，后期仍达峰值）
- **频率提升**: 生成间隔从 1200ms 线性衰减至 500ms
- 以上曲线用于制造“逐渐紧张”的节奏，但保持轻量以避免性能抖动

### 5. UI 布局
- **顶部栏**: 剩余时间（倒计时）+ 当前分数
- **游戏区**: 中部为汤圆运动区域
 - **底部**: 固定碗（页面最底部，宽度=屏幕宽度，高度自适应，图片不变形；推荐 `image` 组件 `mode="widthFix"`）
- **禁止项**: 不使用悬浮控件；不提供暂停按钮

### 6. 交互与视觉反馈（低消耗）
- **入碗反馈**: 触发轻微缩放（如 scale 1.0→1.1→1.0，持续 ≤ 120ms）+ 分数飘字（opacity/translateY，持续 ≤ 300ms，位置以碗上方为准）；入碗后汤圆停留在碗内并冻结
- **未入碗**: 越界自动淡出（opacity 1→0，≤ 120ms），避免重计算
- **涟漪/粒子**: 若需要，仅保留 1 个简化涟漪（纯 CSS 圆环 scale+opacity，≤ 200ms）；避免阴影/模糊滤镜
- **无音效/无震动**

### 7. 资源与预加载
- **图片资源（云端 URL）**
    - 背景：`https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/game/tangyuan/bg.jpg`
    - 汤圆：`https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/game/tangyuan/tangyuan.png`
    - 碗：`https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/game/tangyuan/bowl.png`

 - **分辨率与渲染约束**
     - 汤圆源图分辨率：120×120（正方形）。渲染时保持正方形，显示尺寸可按体验设定
     - 碗源图分辨率：640×230。渲染在页面底部，宽度=屏幕宽度，高度按比例自适应，确保不变形


### 8. 技术实现建议（避免 canvas 的方案）
- **渲染方式**: 使用普通节点 `<view>` 配合 CSS `transform: translate3d` 与过渡/关键帧动画实现横向移动
- **生成与销毁**:
    - 定时生成实例（基于 `setInterval` 或 `setTimeout` 串联），为每个汤圆附加动画 class，从右→左运动
    - 到达左边界时触发 `animationend`/基于计时的回收逻辑，移除 DOM 节点
- **拖拽逻辑**:
    - `touchstart` 点击命中汤圆：移除其运动动画，标记为拖拽态；位置改为绝对定位跟随手指
    - `touchmove` 跟随移动（节流到 ~16–32ms/次，减少重排）
    - `touchend` 进行一次性入碗判定；成功则得分并播放入碗反馈，随后将该汤圆标记为 `inBowl` 状态并冻结（取消交互与动画，保持在碗内，不再移动/回收）；失败则恢复左移动画
- **碰撞/判定**:
    - 进入页面缓存碗的几何信息（`uni.createSelectorQuery().select('#bowl').boundingClientRect`）
    - 汤圆大小一致，判定规则：AABB 交叠阈值判定，允许部分超出碗底也视为成功。示例阈值：水平/垂直方向的重叠长度均 ≥ 汤圆尺寸的 20%；入碗后将坐标钳制在碗内部
- **性能约束**:
    - 同屏“活跃（移动或可交互）”汤圆 ≤ 8；已入碗且冻结的汤圆不计入该限制，但需控制整体 DOM 数量，保持流畅
    - 避免使用 `box-shadow`、`filter: blur` 等高耗特效；仅用 `transform`/`opacity`
    - 触摸事件统一节流；仅在 `touchend` 时做碰撞判定
 - **Y 轴生成范围实现**:
     - 进入页面后计算 Y 范围：从屏幕高度的 1/5 到“碗的上边缘 − 汤圆渲染高度一半”，在该区间内生成随机 Y 值
     - 计算需考虑顶部栏的占位，避免与顶部 UI 重叠

### 9. 配置常量（示例，可按需微调）
```js
export const TANGYUAN_CONFIG = {
    // 基本
    durationSec: 30,                   // 游戏总时长（单位：秒）
    initialSpawnIntervalMs: 1200,      // 初始生成间隔
    minSpawnIntervalMs: 500,           // 最小生成间隔
    initialSpeedPxPerSec: 160,         // 初始水平速度
    maxSpeedPxPerSec: 520,             // 最大水平速度（峰值）
    speedCurveGamma: 1.6,              // 速度曲线幂指数（降低以加快三分之二进度时的速度）
    maxConcurrentItems: 8,             // 同屏最大汤圆数量

    // 判定
    overlapThreshold: 0.6,             // AABB 面积重叠阈值（或圆心入碗口）
    tangyuanRenderSizePx: 80,          // 汤圆渲染尺寸（保持正方形，可按体验微调）
    tangyuanAssetSizePx: 120,          // 汤圆源图尺寸（120×120，仅用于说明）

    // 动效（尽量轻量）
    inBowlScaleMs: 120,                // 入碗缩放时间
    scoreFloatMs: 300,                 // 飘字时间
    missFadeMs: 120,                   // 未入碗淡出时间
}
```

#### Vue2 页面使用建议（data 中可自定义）
```js
export default {
    data() {
        return {
            // 直接放入 data，便于后续动态自定义/联动表单
            config: {
                durationSec: 30,
                initialSpawnIntervalMs: 1200,
                minSpawnIntervalMs: 500,
                initialSpeedPxPerSec: 160,
                maxSpeedPxPerSec: 360,
                maxConcurrentItems: 8,
                overlapThreshold: 0.6,
                tangyuanRenderSizePx: 96
            }
        }
    }
}
```

### 10. 验收标准
- 进入页面 ≤ 2s 可见并可操作
- 倒计时精确：30s（误差 ≤ 200ms）
- 在常见机型上帧率 ≥ 30 FPS，操作无明显延迟
- 生成频率与速度在 30s 内有明确提升；同时不出现明显卡顿
- 拖拽入碗判定准确；入碗后汤圆留在碗内且不可再拖动；未入碗不扣分、越界正常回收
 - 拖拽入碗判定准确（碗内部矩形含安全边距，拖到碗底部也应判定成功并留在碗内）；入碗后汤圆留在碗内且不可再拖动；未入碗不扣分、越界正常回收
- 全程不可暂停；无悬浮控件；无音效
 - 汤圆 Y 轴随机范围：屏幕高度的 1/5 到碗上边缘之间（需扣除汤圆半径，避免视觉穿插）
 - 汤圆渲染保持正方形；碗宽度=屏幕宽度，高度自适应且不变形
 - 分数飘字位置以碗上方为准
 - 关键配置（时长、频率、速度、尺寸、阈值等）可在 Vue2 `data()` 中直接调整并生效

### 11. 文件与目录
- 代码主文件：`pages/games/tangyuan-catcher/tangyuan-catcher.vue`
- 需求文档：`pages/games/tangyuan-catcher/game.md`
- 图片资源：沿用云端 URL（无需落地 `static/`）
