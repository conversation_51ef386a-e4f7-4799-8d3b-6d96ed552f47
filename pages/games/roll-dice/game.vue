<template>
    <view>
        <roll-dice :allow-roll="allowRoll" @rollOver="rollOver"/>
        <game-tips-popup ref="gameTipsPopup" :tips-list="tipsList" :show-ad="showAD" @startGame="startGame"/>
        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>
    </view>
</template>

<script>
import gameTipsPopup from '../components/game-tips-popup.vue'
import rollDice from './roll-dice.vue'

export default {
    components: {gameTipsPopup, rollDice},
    data() {
        return {
            allowRoll: false,
            per_integral: 0,
            unit: '积分',
            showAD: false,
            diceValues: [0, 0, 0, 0, 0, 0]
        }
    },

    computed: {
        tipsList() {
            return [
                '🎲 同时掷出六枚神奇骰子',
                `🎯 骰子停止滚动后计算点数总和，每一点可获得${this.per_integral}${this.unit}`
            ]
        },

        diceTotalCount() {
            return this.diceValues.reduce((acc, val) => acc + val, 0)
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        this.point_id = params.point_id
        this.per_integral = params.per_integral
        this.unit = params.unit
        if (params.show_ad) this.showAD = true
        if (params.title) this.$uni.setNavigationBarTitle(params.title)

        this.init()
    },

    methods: {
        async init() {
            this.$refs.gameTipsPopup.open()
        },


        startGame() {
            this.$refs.gameTipsPopup.close()
            this.allowRoll = true
        },

        rollOver(values) {
            this.diceValues = values
            this.allowRoll = false

            this.$nextTick(() => this.submitResult())
        },

        async submitResult() {
            const count = this.diceTotalCount
            const sign = {
                types: 46,
                point_id: this.point_id,
                result: 'success',
                count
            }

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.active_id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify(sign))
                }
            })
            uni.hideLoading()

            let resultCode = sign.result === 'success' ? 1 : 0
            let info = resultCode === 1 ? '任务完成' : '任务失败'
            if (res?.status !== 1) {
                resultCode = 0
                if (res?.info) info = res.info
            }

            this.$refs.resultPopup.open({
                code: resultCode,
                integral: res.data.num || null,
                info
            })


            this.getOpenerEventChannel().emit('success')
        },
    },
}
</script>

<style scoped lang="scss">

</style>
