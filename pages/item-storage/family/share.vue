<template>
    <view>
        <view v-if="ownerInfo.headimg" class="headimg-container flex-all-center">
            <image :src="ownerInfo.headimg" class="headimg-image" mode="aspectFill"/>
        </view>

        <view class="text-center color-title p10">{{ ownerInfo.nickname || '你的亲友' }}邀请你一起管理TA的物品</view>

        <view class="p10">
            <view class="color-content pb10">你的昵称:</view>
            <uni-easyinput v-model="nickname" maxlength="10" placeholder="请输入你的昵称以便于亲友知道你是谁"/>
        </view>

        <view class="accept-button" @click="handleAcceptInvite">接受邀请</view>
    </view>
</template>

<script>
const app = getApp()

export default {
    data() {
        return {
            // 邀请者信息
            ownerInfo: {
                nickname: '',
                headimg: ''
            },
            // 当前用户输入的昵称
            nickname: '',
        }
    },
    
    onLoad(params) {
        this.uuid = params.uuid
        this.types = Number(params.types)

        this.$uni.showLoading()
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    methods: {
        async init() {
            await this.getOwnerInfo()
            uni.hideLoading()
        },

        // 获取邀请者信息
        async getOwnerInfo() {
            const res = await this.xwy_api.request({
                url: 'front.user.user/user_details',
                data: {
                    userid: this.uuid
                }
            })

            const info = res?.data?.user_details
            if (!info?.id) {
                uni.hideLoading()
                this.$uni.showModal('邀请失效', {success: () => this.$uni.reLaunch('/pages/item-storage/item/list')})
                return
            }

            this.ownerInfo = {
                nickname: info.nickname || '',
                headimg: info.headimg || ''
            }
        },

        // 处理接受邀请
        async handleAcceptInvite() {
            // 验证昵称输入
            if (!this.nickname.trim()) {
                this.$uni.showToast('请输入你的昵称')
                return
            }

            const uuid = app.globalData.userinfo.userid
            if (uuid === this.uuid) {
                this.$uni.showToast('不能邀请自己')
                return
            }

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.user.account.shareManageRank/create_user_rank',
                data: {
                    belong_userid: this.uuid,
                    types: this.types,
                    nickname: this.nickname
                }
            })
            uni.hideLoading()

            if (res?.status === 1) {
                this.$uni.showToast('加入成功！')
                setTimeout(() => {
                    this.$uni.reLaunch('/pages/item-storage/item/list')
                }, 1500)
                return
            }

            this.$uni.showModal(res?.info || '加入失败')
        }
    }
}
</script>

<style lang="scss" scoped>
.headimg-container {
    padding: 15vh 0 50px 0;

    .headimg-image {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        display: block;
    }
}

.accept-button {
    margin: 50px auto;
    width: 150px;
    line-height: 44px;
    border-radius: 22px;
    background-color: #5cadff;
    color: #fff;
    text-align: center;
}
</style>
