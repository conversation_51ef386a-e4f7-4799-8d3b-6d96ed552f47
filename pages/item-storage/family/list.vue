<template>
    <view class="page">
        <!-- <view class="type-bar">
            <view class="type-bar-container flex-row">
                <view class="type-item" :class="{ 'active-type': item.type === activeType }"
                    :style="{ width: `calc(100% / ${typeOptions.length})` }" v-for="item in typeOptions"
                    :key="item.type" @click="handleTypeChange(item.type)">
                    {{ item.title }}
                </view>
            </view>
        </view> -->

        <view v-if="!loading && total" class="color-sub font14 text-center p10">
            <template v-if="activeType === 1">共邀请{{ total }}位亲友管理你的物品</template>
            <template v-else-if="activeType === 2">共管理{{ total }}位亲友的物品，点击亲友头像即可切换管理亲友物品</template>
        </view>


        <view v-if="!loading || total" class="family-list">
            <view v-if="activeType === 2" class="family-item flex-row" @click="handleSelectFriend(null)">
                <view class="headimg-container flex-all-center">
                    <image :src="myInfo.headimg" class="headimg-image" mode="aspectFill" />
                </view>

                <view class="family-info">
                    <view class="color-title">
                        <text>{{ myInfo.nickname }}</text>
                        <text class="color-sub font14 pl5">(我自己)</text>
                    </view>
                </view>
                
                <view v-if="!currentManagedFriend" class="selected-icon flex-all-center">
                    <uni-icons type="checkmarkempty" size="20" color="#5cadff"/>
                </view>
            </view>

            <view class="family-item flex-row" v-for="item in familyList" :key="item.id" @click="activeType === 2 ? handleSelectFriend(item) : null">

                <view class="headimg-container flex-all-center">
                    <image :src="item.headimg" class="headimg-image" mode="aspectFill" />
                </view>

                <view class="family-info">
                    <view class="color-title">{{ item.nickname }}</view>
                    <view class="color-sub font14">{{ item.create_time }}</view>
                </view>

                <view v-if="activeType === 1" class="delete-icon font14" @click.stop="handleDelete(item)">
                    <text class="iconfont icon-delete color-sub"></text>
                </view>
                
                <view v-if="activeType === 2 && currentManagedFriend && currentManagedFriend.uuid === item.uuid" class="selected-icon flex-all-center">
                    <uni-icons type="checkmarkempty" size="20" color="#5cadff"/>
                </view>
            </view>
        </view>

        <view v-if="activeType === 1 && !loading && !total" class="empty-list text-center">
            <text class="iconfont icon-team color-border"></text>
            <view class="color-sub font14">暂无已邀请的亲友</view>

            <button class="share-button" open-type="share">邀请亲友一起管理</button>
        </view>

        <view v-if="loading" class="text-center" :style="{ paddingTop: currentPage === 1 ? '30vh' : '0' }">
            <load-ani />
            <view v-if="currentPage === 1" class="color-sub font12">加载中...</view>
        </view>

        <view v-if="activeType === 1 && !loading && total" class="bottom-share-button">
            <button class="share-button" open-type="share">邀请亲友一起管理</button>
        </view>
    </view>
</template>

<script>
import _API_ from '../api'
const app = getApp()

export default {
    data() {
        return {
            typeOptions: [
                { type: 2, title: '我管理的' },
                { type: 1, title: '我邀请的' }
            ],
            activeType: 2,

            loading: true,
            familyList: [],
            currentPage: 1,
            pageSize: 20,
            total: 0,
            isLastPage: false,

            myInfo: {
                nickname: '',
                headimg: ''
            },
            
            currentManagedFriend: null // 当前管理的亲友
        }
    },

    computed: {
        typeTitle() {
            return this.typeOptions.find(item => item.type === this.activeType).title
        }
    },

    onLoad(params) {
        if (params.type) this.activeType = Math.floor(params.type)
        this.$login.uniLogin(() => {
            this.init()
        })
    },

    onPullDownRefresh() {
        this.currentPage = 1
        this.getFamilyList().finally(() => uni.stopPullDownRefresh())
    },

    onReachBottom() {
        if (this.loading || this.isLastPage) return
        this.currentPage++
        this.getFamilyList()
    },

    methods: {
        async init() {
            const { nickname, headimg } = app.globalData.userinfo
            this.myInfo.nickname = nickname
            this.myInfo.headimg = headimg
            
            // 获取当前管理的亲友信息
            this.currentManagedFriend = _API_.getCurrentManagedFriend()

            await this.getFamilyList()
        },

        // handleTypeChange(type) {
        //     if (type === this.activeType) return
        //     this.activeType = type
        //     this.currentPage = 1
        //     this.getFamilyList()
        // },

        async getFamilyList() {
            if (this.currentPage === 1) {
                this.total = 0
                this.familyList = []
            }

            const data = { page: this.currentPage, perpage: this.pageSize }
            // 被邀请
            if (this.activeType === 2) data.is_helper = 1

            this.loading = true
            const res = await this.xwy_api.request({
                url: 'front.user.account.shareManageRank/share_manage_user_list',
                data
            })
            this.loading = false

            let total = 0, list = [], isLastPage = false

            if (res?.status === 1 && res?.data?.list) {
                const result = res.data.list
                total = result.total || 0
                list = this.initListData(result.data || [])
                isLastPage = result.is_lastpage
            } else {
                isLastPage = true
            }

            this.total = total
            this.familyList = this.currentPage === 1 ? list : [...this.familyList, ...list]
            this.isLastPage = isLastPage
        },

        initListData(list) {
            return list.map(item => {
                const details = (this.activeType === 1 ? item.user_details : item.belong_user_details) || {}

                return {
                    id: item.id,
                    userid: details.id,
                    uuid: details?.uuid || '',
                    create_time: item.create_time,
                    nickname: details.nickname || '',
                    headimg: details.headimg || '',
                }
            })
        },

        async handleDelete(item) {
            const { confirm } = await this.$uni.showModal(`确定移除${item.nickname}的管理权限?`, { showCancel: true })
            if (!confirm) return

            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.user.account.shareManageRank/del_share_user',
                data: {
                    ids: item.id
                }
            })
            uni.hideLoading()

            if (res?.status !== 1) {
                this.$uni.showModal(res?.info || '移除失败')
                return
            }

            this.$uni.showToast('移除成功')
            this.currentPage = 1
            this.getFamilyList()
        },
        
        // 选择要管理的亲友
        async handleSelectFriend(friend) {
            if (this.activeType !== 2) return
            
            // 如果选择的是当前已选中的亲友，不做处理
            if (!friend && !this.currentManagedFriend) return
            if (friend && this.currentManagedFriend && friend.uuid === this.currentManagedFriend.uuid) return
            
            if (friend) {
                // 获取亲友的详细信息
                this.$uni.showLoading('切换中...')
                const friendData = await _API_.getFriendDetails(friend.uuid)
                uni.hideLoading()
                
                if (!friendData) {
                    this.$uni.showToast('获取亲友信息失败')
                    return
                }
                
                // 设置当前管理的亲友（只保存uuid）
                _API_.setCurrentManagedFriend(friend)
                
                // 设置详细数据到内存
                _API_.currentManagedFriend = friendData.share_friend_details
                _API_.currentManagedFriendData = friendData
                
                this.$uni.showToast(`已切换至管理${friend.nickname}的物品`)
            } else {
                // 切换回自己
                _API_.setCurrentManagedFriend(null)
                this.$uni.showToast('已切换至管理自己的物品')
            }
            
            // 更新当前状态
            this.currentManagedFriend = _API_.getCurrentManagedFriend()
            
            this.getOpenerEventChannel?.()?.emit?.('familyChange')

            this.$uni.navigateBack(1, {delay: 500})
        }
    },

    onShareAppMessage() {
        return {
            title: '一起来管理我的物品吧',
            path: `/pages/item-storage/family/share?types=1&uuid=${app.globalData.userinfo.userid}`,
            imageUrl: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/small-feature-collection/item-storage.png'
        }
    }
}
</script>

<style lang="scss" scoped>
$page-bg-color: #f8f8f8;

.page {
    box-sizing: border-box;
    min-height: 100vh;
    background-color: $page-bg-color;
    // padding-top: 80px;
    padding-bottom: 70px;
}

// .type-bar {
//     position: fixed;
//     top: 0;
//     left: 0;
//     z-index: 99;
//     background-color: $page-bg-color;
//     padding: 20px 40px;
//     width: 100vw;
//     box-sizing: border-box;

//     .type-bar-container {
//         width: 100%;
//         border-radius: 20px;
//         overflow: hidden;
//         background-color: #fff;
//         box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);

//         .type-item {
//             height: 40px;
//             line-height: 40px;
//             text-align: center;
//             color: #333;

//             &.active-type {
//                 color: #fff;
//                 background-color: #5cadff;
//                 border-radius: 20px;
//             }
//         }
//     }
// }

.family-list {
    .family-item {
        margin: 10px;
        padding: 10px;
        border-radius: 10px;
        background-color: #fff;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
        position: relative;

        .headimg-container {
            padding-right: 10px;

            .headimg-image {
                width: 60px;
                height: 60px;
                display: block;
                border-radius: 50%;
            }
        }

        .family-info {
            padding: 2px 0;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .delete-icon {
            position: absolute;
            right: 10px;
            bottom: 10px;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            border: 1px solid #e5e5e5;
            margin-left: 5px;
        }
        
        .selected-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}

.share-button {
    line-height: 40px;
    border-radius: 20px;
    background-color: #5cadff;
    color: #fff;
    font-size: 14px;

    &::after {
        border: none;
    }
}

.empty-list {
    padding-top: 30vh;

    .iconfont {
        font-size: 100px;
    }

    .share-button {
        margin: 50px auto 0;
        width: 200px;
    }
}

.bottom-share-button {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 99;
    background-color: $page-bg-color;
    padding: 15px;
    width: 100vw;
    box-sizing: border-box;

    .share-button {
        width: 100%;
    }
}
</style>
