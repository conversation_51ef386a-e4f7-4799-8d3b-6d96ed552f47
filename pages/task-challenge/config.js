export default {
    getDefaultIconsSet() {
        const options = [
            {name: 'set', title: '活动设置'},
            {name: 'user_info', title: '报名信息'},
            {name: 'ranking_list', title: '排行榜'},
            {name: 'exam', title: '答题'},
            {name: 'sport_comment', title: '运动圈'},
            {name: 'active_rule', title: '活动说明'},
            {name: 'shopping', title: '积分商城'},
            {name: 'lottery', title: '抽奖'},
            {name: 'certificate', title: '证书'},
            {name: 'article_reading', title: '文章阅读'},
            {name: 'active_notice', title: '活动须知'},
            {name: 'user_center', title: '个人中心'},
            {name: 'share', title: '转发分享'},
            {name: 'open_more', title: '展开更多'},
            {name: 'close_more', title: '关闭更多'}
        ]
        
        return options.map(item => {
            return {
                ...item,
                type: 'system',
                image: '',
                hide: 0
            }
        })
    },
    
    getTaskActiveIconsSet(icons_set) {
        // 如果有新需求，要添加新icon的话，判断所有icon配置在保存的设置里面有没有，没有就添加
        this.getDefaultIconsSet().forEach(icons => {
            if (!icons_set.some(item => item.name === icons.name)) {
                // 不能使用push，因为最后两个“展开、收起”必须是在最后，所以在头部添加
                icons_set.unshift(icons)
            }
        })
        return icons_set
    },
    
    AI_SPORT_DEFAULT_DATA: {
        types: '',
        name: '',
        integral_reward: {
            num: '',
            integral: '',
            max_daily: ''
        },
        logo: ''
    },
    
    ALL_JOB: [
        {types: 1, name: '运动步数'},
        {types: 2, name: '答题考试'},   // OA 答题
        {types: 3, name: '发布运动圈'}, // OA 运动圈
        {types: 4, name: 'AI运动'},    // OA AI运动
        {types: 5, name: '文章阅读'},   // OA 文章阅读奖励
        {types: 6, name: '每日签到'},   // OA 签到
        {types: 7, name: '在线拼图'},
        {types: 8, name: '事件排序'},
        {types: 9, name: '党建填词'},   // OA 答题
        {types: 10, name: '红色扫雷'},
        {types: 11, name: '连连看'},
        {types: 12, name: '抢粮食'},
        {types: 13, name: '贪吃蛇'},
        {types: 14, name: '找茬'},
        {types: 15, name: '成语接龙'},
        {types: 16, name: '打地鼠'},
        
        // 这写任务需要在JSON里面设置才能用
        /*{types: 18, name: '垃圾分类游戏'},
        {types: 19, name: '转发运动圈'},
        {types: 20, name: '转发文章'},*/
        
        
        {types: 21, name: '照片合成'},
        {types: 22, name: '连红包'},
        {types: 23, name: '打年兽'},
        //{"types":23,  "logo":"设置的图标logo",   "title":"打年兽-完成获得积分","text":"打年兽-打中一次获得积分数为1分，打中的越多奖励越多，每天可完成2次。限制时间50秒内必需完成。","count":2,"per_integral":1,"seconds":50"},
        
        // 24 飞机大战    // 飞机大战   分数
        {types: 24, name: '飞机大战'},
        // 25  消消乐    // 消消乐   分数 积分   按照游戏的玩法，这个游戏应该叫做翻翻乐才对
        {types: 25, name: '翻翻乐'},
        // 26 羊了个羊  // 羊了个羊   消掉的数量
        {types: 26, name: '羊了个羊'},
        
        
        {types: 27, name: '体重'},
        {types: 28, name: '体脂'},
        {types: 29, name: '腰围'},
        
        // 这些任务需要在JSON里面设置才能用
        // {types: 30, name: '云祝福'},
        
        {types: 31, name: '每周运动(瑜伽、跳绳等)'},
        {types: 32, name: '每周步数'},
        {types: 33, name: '每周力量训练'},
        {types: 34, name: '每周团体运动(羽毛球、乒乓球等)'},
        
        // 35 饮食圈-上传饮食拍照打卡识别卡路里获得积分奖励
        // {types: 35, name: '饮食圈'},
        
        {types: 36, name: '数钱'},
        {types: 37, name: '绕口令'},
        {types: 38, name: '点球大战'},
        {types: 39, name: '小马快跑'},
        {types: 40, name: '消消乐'},
        {types: 41, name: '打爆气球'},
        {types: 42, name: '重力跳一跳'},
        // {types: 43, name: '搬砖'},
        // {types: 44, name: '颜色游戏'},

        {types: 45, name: '动态圈'},  // OA 运动圈

        {types: 46, name: '摇骰子'},
        // {types: 47, name: '抢汤圆'},
        // {types: 48, name: '点击做俯卧撑'},
    ],
    
    needOaOpen: [2, 3, 4, 5, 6, 9, 45],
    
    getActiveJobListSet(activeDetails = {}, activeConfSet = {}) {
        const {AI_motion, exam_open, morning_daily_sign, open_sport_moment, reading_reward} = activeDetails.rank_set || {}
        
        const list = this.ALL_JOB.filter(item => {
            if ((item.types === 2 || item.types === 9) && exam_open) return true
            if ((item.types === 3 || item.types === 45) && open_sport_moment) return true
            if (item.types === 4 && AI_motion) return true
            if (item.types === 5 && reading_reward) return true
            if (item.types === 6 && morning_daily_sign) return true
            
            return !this.needOaOpen.includes(item.types)
        })
        
        const {round_rush_job_show_list = []} = activeConfSet
        
        return [...list, ...round_rush_job_show_list]
    },
    
    needIntegralTypes: [1, 3, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 45, 46],
    needCountTypes: [3, 5, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26, 30, 36, 37, 38, 39, 40, 41, 42, 45, 46],
    needSecondsTypes: [15, 16, 18, 22, 23, 24, 25, 26, 36, 37, 38, 39, 40, 41, 42],
    needMaxCountTypes: [15, 16],
    needMaxIntegralTypes: [2, 4, 5, 11, 18, 28, 29],
    needWeekCount: [31, 33, 34],
    // needAllTypesMax: [31, 32, 33, 34],
    
    
    getDefaultStartButtonSet() {
        return {
            bg_color: 'rgba(243, 187, 58, 1)',
            text_color: 'rgba(255, 255, 255, 1)',
            text: '开始闯关'
        }
    },
    
    lianLianKanDefaultSet: {
        row: 10,
        col: 6,
        img_count: 10,
        seconds: ''
    },
    
    gluttonousSnakeDefaultSet: {
        count: 10,
        seconds: 60,
        level: 1
    },
    
    gluttonousSnakeLevelList: [
        {title: '入门', value: 1},
		{title: '简单', value: 2},
		{title: '一般', value: 3},
		{title: '困难', value: 4}
	],
    
    taskPopupDefaultStyle() {
        return {
            border_color: 'rgba(255,214,122, 1)',
            bg_color: 'rgba(255, 255, 255, 1)',
            text_color: 'rgba(128, 132, 143, 1)',
            task_item: {
                border_color: 'rgba(254, 237, 205, 1)',
                bg_color: 'rgba(255, 253, 250, 1)',
                name_color: 'rgba(28, 35, 55, 1)',
                text_color: 'rgba(128, 132, 143, 1)',
                integral_color: 'rgba(255, 155, 4, 1)',
                button: {
                    completed: {
                        bg_color: 'rgba(83, 157, 140, 1)',
                        text_color: 'rgba(255, 255, 255, 1)',
                    },
                    uncompleted: {
                        bg_color: 'rgba(243, 187, 58, 1)',
                        text_color: 'rgba(255, 255, 255, 1)',
                    }
                }
            }
        }
    },
    
    integralTypesConfig: {
        '31': 34,
        '33': 36,
        '34': 37
    },
    
    getTaskIntegralTypes(types) {
        return this.integralTypesConfig[types] || 0
    },
}